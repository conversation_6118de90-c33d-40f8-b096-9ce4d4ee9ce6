use std::collections::HashMap;
use std::io::{self, Write};

use crate::command_planner::{CommandPlanner, CommandContext};
use crate::terminal_bridge::{TerminalBridge, ExecutionConfig};
use crate::command_memory::CommandMemory;
use crate::security_layer::SecurityLayer;
use crate::logger::LOGGER;
use crate::config::Config;
use crate::language::Language;

pub struct InteractiveSession {
    planner: CommandPlanner,
    bridge: TerminalBridge,
    memory: CommandMemory,
    security: SecurityLayer,
    config: Config,
    current_language: Language,
    session_vars: HashMap<String, String>,
    command_aliases: HashMap<String, String>,
    auto_complete_enabled: bool,
    session_id: String,
}

#[derive(Debug, Clone)]
pub struct SessionConfig {
    pub enable_auto_complete: bool,
    pub enable_command_chaining: bool,
    pub enable_history_search: bool,
    pub max_chain_length: usize,
    pub prompt_style: PromptStyle,
}

#[derive(Debug, Clone)]
pub enum PromptStyle {
    Simple,
    Detailed,
    Minimal,
    Custom(String),
}

impl Default for SessionConfig {
    fn default() -> Self {
        Self {
            enable_auto_complete: true,
            enable_command_chaining: true,
            enable_history_search: true,
            max_chain_length: 5,
            prompt_style: PromptStyle::Detailed,
        }
    }
}

impl InteractiveSession {
    pub fn new(config: Config) -> Result<Self, Box<dyn std::error::Error>> {
        let current_language = Language::from_str(&config.default_language)
            .unwrap_or(Language::Python);
        
        let context = CommandContext {
            current_directory: std::env::current_dir()?.to_string_lossy().to_string(),
            available_tools: vec!["python3".to_string(), "git".to_string(), "ls".to_string()],
            recent_commands: Vec::new(),
            project_type: Some("rust".to_string()),
            language_context: Some(current_language.to_string()),
        };
        
        let planner = CommandPlanner::new(context);
        
        let execution_config = ExecutionConfig {
            timeout_seconds: config.default_timeout,
            capture_output: true,
            working_directory: None,
            environment_vars: HashMap::new(),
            max_output_lines: Some(100),
            stream_output: false,
        };
        
        let bridge = TerminalBridge::new(execution_config);
        
        let config_dir = Config::get_config_path()?.parent().unwrap().to_path_buf();
        let data_dir = config_dir.join("data");
        let memory = CommandMemory::new(data_dir.clone())?;

        let security = SecurityLayer::new(config_dir)?;

        let session_id = format!("session_{}",
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs()
        );

        Ok(Self {
            planner,
            bridge,
            memory,
            security,
            config,
            current_language,
            session_vars: HashMap::new(),
            command_aliases: Self::default_aliases(),
            auto_complete_enabled: true,
            session_id,
        })
    }

    fn default_aliases() -> HashMap<String, String> {
        let mut aliases = HashMap::new();
        aliases.insert("ll".to_string(), "ls -la".to_string());
        aliases.insert("la".to_string(), "ls -la".to_string());
        aliases.insert("..".to_string(), "cd ..".to_string());
        aliases.insert("cls".to_string(), "clear".to_string());
        aliases.insert("h".to_string(), "history".to_string());
        aliases.insert("q".to_string(), "quit".to_string());
        aliases
    }

    pub fn run(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        LOGGER.welcome();
        LOGGER.info(&format!("Interactive session started (ID: {})", self.session_id));
        LOGGER.info("Enhanced terminal interaction mode - Type 'help' for commands, 'quit' to exit");
        LOGGER.separator();

        self.show_session_info();

        loop {
            self.display_prompt();
            
            let mut input = String::new();
            match io::stdin().read_line(&mut input) {
                Ok(_) => {
                    let command = input.trim();
                    
                    if command.is_empty() {
                        continue;
                    }
                    
                    // Handle exit commands
                    if matches!(command.to_lowercase().as_str(), "exit" | "quit" | "q") {
                        LOGGER.goodbye();
                        break;
                    }
                    
                    // Process the command
                    if let Err(e) = self.process_command(command) {
                        LOGGER.error(&format!("Command processing error: {}", e));
                    }
                }
                Err(error) => {
                    LOGGER.error(&format!("Error reading input: {}", error));
                    break;
                }
            }
        }
        
        Ok(())
    }

    fn display_prompt(&self) {
        let current_dir = std::env::current_dir()
            .map(|p| p.file_name().unwrap_or_default().to_string_lossy().to_string())
            .unwrap_or_else(|_| "unknown".to_string());
        
        print!("\n🤖 [{}:{}] {} ", 
            self.current_language.to_string().to_lowercase(),
            current_dir,
            ">".bright_blue()
        );
        io::stdout().flush().unwrap();
    }

    fn show_session_info(&self) {
        LOGGER.section("Session Information");
        println!("  Language: {}", self.current_language);
        println!("  Model: {}", self.config.get_effective_model(&self.current_language));
        println!("  Timeout: {}s", self.config.get_effective_timeout(&self.current_language));
        println!("  Testing: {}", if self.config.get_effective_testing(&self.current_language) { "Enabled" } else { "Disabled" });
        println!("  Auto-complete: {}", if self.auto_complete_enabled { "Enabled" } else { "Disabled" });
    }

    fn process_command(&mut self, input: &str) -> Result<(), Box<dyn std::error::Error>> {
        // Handle command chaining (commands separated by &&, ||, or ;)
        if input.contains("&&") || input.contains("||") || input.contains(";") {
            return self.process_command_chain(input);
        }

        // Expand aliases
        let expanded_input = self.expand_aliases(input);
        
        // Handle built-in commands
        if self.handle_builtin_command(&expanded_input)? {
            return Ok(());
        }

        // Handle simple shell commands directly (for quick operations)
        if self.is_simple_shell_command(&expanded_input) {
            let result = self.bridge.execute_interactive_command(&expanded_input);
            if result.success {
                if !result.stdout.is_empty() {
                    println!("{}", result.stdout);
                }
            } else {
                LOGGER.error(&format!("Command failed: {}", result.stderr));
            }
            return Ok(());
        }

        // Show suggestions if enabled
        if self.auto_complete_enabled {
            let suggestions = self.memory.get_suggestions(&expanded_input);
            if !suggestions.is_empty() && suggestions[0] != expanded_input {
                println!("💡 Suggestions: {}", suggestions.join(", "));
            }
        }

        // Plan the command
        let plan = self.planner.plan_command(&expanded_input);
        
        // Show plan summary
        LOGGER.info(&format!("Intent: {} | Risk: {} | Commands: {}", 
            plan.intent, plan.risk_level, plan.commands.len()));

        // Check if confirmation is needed
        if self.memory.should_confirm(&plan) {
            if !self.memory.request_confirmation(&plan) {
                LOGGER.info("Command execution cancelled by user");
                return Ok(());
            }
        }

        // Execute the plan with progress tracking
        let total_commands = plan.commands.len();
        if total_commands > 1 {
            LOGGER.info(&format!("Executing {} commands...", total_commands));
        }

        let results = self.bridge.execute_plan(&plan);

        // Display results
        for (i, result) in results.iter().enumerate() {
            if total_commands > 1 {
                LOGGER.progress_bar(i + 1, total_commands, &format!("Command {}/{}", i + 1, total_commands));
            }
            if result.success {
                if !result.stdout.is_empty() {
                    LOGGER.output_header();
                    println!("{}", result.stdout);
                }
            } else {
                LOGGER.error_header();
                if !result.stderr.is_empty() {
                    println!("{}", result.stderr);
                }
            }
        }

        // Add to memory
        self.memory.add_execution(expanded_input, plan, results);
        
        Ok(())
    }

    fn process_command_chain(&mut self, input: &str) -> Result<(), Box<dyn std::error::Error>> {
        LOGGER.info("Processing command chain");
        
        // Simple chain parsing (can be enhanced)
        let commands: Vec<&str> = if input.contains("&&") {
            input.split("&&").collect()
        } else if input.contains("||") {
            input.split("||").collect()
        } else {
            input.split(";").collect()
        };

        for (i, cmd) in commands.iter().enumerate() {
            let cmd = cmd.trim();
            if cmd.is_empty() {
                continue;
            }
            
            LOGGER.info(&format!("Executing command {}/{}: {}", i + 1, commands.len(), cmd));
            
            if let Err(e) = self.process_command(cmd) {
                LOGGER.error(&format!("Command chain failed at step {}: {}", i + 1, e));
                break;
            }
        }
        
        Ok(())
    }

    fn expand_aliases(&self, input: &str) -> String {
        let parts: Vec<&str> = input.split_whitespace().collect();
        if parts.is_empty() {
            return input.to_string();
        }

        if let Some(alias_expansion) = self.command_aliases.get(parts[0]) {
            if parts.len() > 1 {
                format!("{} {}", alias_expansion, parts[1..].join(" "))
            } else {
                alias_expansion.clone()
            }
        } else {
            input.to_string()
        }
    }

    fn handle_builtin_command(&mut self, input: &str) -> Result<bool, Box<dyn std::error::Error>> {
        let parts: Vec<&str> = input.split_whitespace().collect();
        if parts.is_empty() {
            return Ok(false);
        }

        match parts[0] {
            "help" => {
                self.show_enhanced_help();
                Ok(true)
            }
            "history" => {
                let limit = if parts.len() > 1 {
                    parts[1].parse().ok()
                } else {
                    None
                };
                self.memory.show_history(limit);
                Ok(true)
            }
            "alias" => {
                if parts.len() == 3 {
                    self.command_aliases.insert(parts[1].to_string(), parts[2].to_string());
                    LOGGER.success(&format!("Alias created: {} -> {}", parts[1], parts[2]));
                } else {
                    println!("Current aliases:");
                    for (alias, command) in &self.command_aliases {
                        println!("  {} -> {}", alias, command);
                    }
                }
                Ok(true)
            }
            "set" => {
                if parts.len() == 3 {
                    self.session_vars.insert(parts[1].to_string(), parts[2].to_string());
                    LOGGER.success(&format!("Variable set: {}={}", parts[1], parts[2]));
                } else {
                    println!("Current session variables:");
                    for (key, value) in &self.session_vars {
                        println!("  {}={}", key, value);
                    }
                }
                Ok(true)
            }
            "autocomplete" => {
                if parts.len() > 1 {
                    match parts[1] {
                        "on" | "enable" => {
                            self.auto_complete_enabled = true;
                            LOGGER.success("Auto-complete enabled");
                        }
                        "off" | "disable" => {
                            self.auto_complete_enabled = false;
                            LOGGER.success("Auto-complete disabled");
                        }
                        _ => {
                            LOGGER.error("Usage: autocomplete [on|off]");
                        }
                    }
                } else {
                    println!("Auto-complete is {}", if self.auto_complete_enabled { "enabled" } else { "disabled" });
                }
                Ok(true)
            }
            "status" => {
                self.show_session_info();
                Ok(true)
            }
            "clear" => {
                print!("\x1B[2J\x1B[1;1H"); // ANSI clear screen
                Ok(true)
            }
            "security" => {
                self.handle_security_command(&parts[1..])?;
                Ok(true)
            }
            "ps" => {
                let processes = self.bridge.list_active_processes();
                if processes.is_empty() {
                    LOGGER.info("No active processes");
                } else {
                    println!("Active processes: {:?}", processes);
                }
                Ok(true)
            }
            "kill" => {
                if parts.len() > 1 {
                    if let Ok(pid) = parts[1].parse::<u32>() {
                        match self.bridge.kill_process(pid) {
                            Ok(_) => LOGGER.success(&format!("Process {} terminated", pid)),
                            Err(e) => LOGGER.error(&format!("Failed to kill process {}: {}", pid, e)),
                        }
                    } else {
                        LOGGER.error("Invalid process ID");
                    }
                } else {
                    LOGGER.error("Usage: kill <pid>");
                }
                Ok(true)
            }
            "timeout" => {
                if parts.len() > 1 {
                    if let Ok(timeout) = parts[1].parse::<u64>() {
                        let new_config = crate::terminal_bridge::ExecutionConfig {
                            timeout_seconds: timeout,
                            capture_output: true,
                            working_directory: None,
                            environment_vars: std::collections::HashMap::new(),
                            max_output_lines: Some(100),
                            stream_output: false,
                        };
                        self.bridge.update_config(new_config);
                        LOGGER.success(&format!("Execution timeout updated to {}s", timeout));
                    } else {
                        LOGGER.error("Invalid timeout value");
                    }
                } else {
                    LOGGER.error("Usage: timeout <seconds>");
                }
                Ok(true)
            }
            _ => Ok(false)
        }
    }

    fn handle_security_command(&mut self, args: &[&str]) -> Result<(), Box<dyn std::error::Error>> {
        if args.is_empty() {
            self.security.show_policy();
            return Ok(());
        }

        match args[0] {
            "allow" => {
                if args.len() > 1 {
                    self.security.add_allowed_command(args[1].to_string())?;
                } else {
                    LOGGER.error("Usage: security allow <command>");
                }
            }
            "block" => {
                if args.len() > 1 {
                    self.security.add_blocked_command(args[1].to_string())?;
                } else {
                    LOGGER.error("Usage: security block <command>");
                }
            }
            "unallow" => {
                if args.len() > 1 {
                    self.security.remove_allowed_command(args[1])?;
                } else {
                    LOGGER.error("Usage: security unallow <command>");
                }
            }
            "unblock" => {
                if args.len() > 1 {
                    self.security.remove_blocked_command(args[1])?;
                } else {
                    LOGGER.error("Usage: security unblock <command>");
                }
            }
            "audit" => {
                let limit = if args.len() > 1 {
                    args[1].parse().ok()
                } else {
                    None
                };
                self.security.show_audit_log(limit);
            }
            "dry-run" => {
                if args.len() > 1 {
                    match args[1] {
                        "on" | "enable" => {
                            self.security.enable_dry_run();
                        }
                        "off" | "disable" => {
                            self.security.disable_dry_run();
                        }
                        _ => {
                            LOGGER.error("Usage: security dry-run [on|off]");
                        }
                    }
                } else {
                    let status = if self.security.is_dry_run_enabled() { "enabled" } else { "disabled" };
                    LOGGER.info(&format!("Dry-run mode is {}", status));
                }
            }
            "sandbox" => {
                self.security.create_sandbox_environment()?;
            }
            "help" => {
                self.show_security_help();
            }
            _ => {
                LOGGER.error(&format!("Unknown security command: {}", args[0]));
                self.show_security_help();
            }
        }

        Ok(())
    }

    fn is_simple_shell_command(&self, input: &str) -> bool {
        let simple_commands = [
            "ls", "pwd", "whoami", "date", "echo", "cat", "head", "tail",
            "grep", "find", "which", "uname", "df", "du", "free", "uptime"
        ];

        let first_word = input.split_whitespace().next().unwrap_or("");
        simple_commands.contains(&first_word)
    }

    fn show_security_help(&self) {
        LOGGER.section("Security Commands");
        println!("🔒 Available security commands:");
        println!("  • security - Show current security policy");
        println!("  • security allow <cmd> - Add command to allowlist");
        println!("  • security block <cmd> - Add command to blocklist");
        println!("  • security unallow <cmd> - Remove command from allowlist");
        println!("  • security unblock <cmd> - Remove command from blocklist");
        println!("  • security audit [n] - Show security audit log (last n entries)");
        println!("  • security dry-run [on|off] - Enable/disable dry-run mode");
        println!("  • security sandbox - Create sandbox environment");
        println!("  • security help - Show this help");
    }

    fn show_enhanced_help(&self) {
        LOGGER.section("Enhanced Interactive Commands");
        
        println!("🎯 Code Generation:");
        println!("  • Just type your request: 'create a fibonacci function'");
        println!("  • Chain commands: 'create function && run tests'");
        println!();
        
        println!("🔧 Built-in Commands:");
        println!("  • help - Show this help");
        println!("  • history [n] - Show command history (last n entries)");
        println!("  • status - Show current session status");
        println!("  • alias [name] [command] - Create or list aliases");
        println!("  • set [var] [value] - Set or list session variables");
        println!("  • autocomplete [on|off] - Toggle auto-completion");
        println!("  • clear - Clear screen");
        println!("  • security - Security management commands");
        println!("  • ps - List active processes");
        println!("  • kill <pid> - Terminate a process");
        println!("  • timeout <seconds> - Update execution timeout");
        println!();
        
        println!("⚡ Command Chaining:");
        println!("  • cmd1 && cmd2 - Run cmd2 only if cmd1 succeeds");
        println!("  • cmd1 || cmd2 - Run cmd2 only if cmd1 fails");
        println!("  • cmd1 ; cmd2 - Run both commands sequentially");
        println!();
        
        println!("🎨 Current Aliases:");
        for (alias, command) in &self.command_aliases {
            println!("  • {} -> {}", alias, command);
        }
    }
}

use colored::*; // Add this import for the bright_blue() method
