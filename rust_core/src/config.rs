use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};
use std::env;
use crate::language::Language;
use crate::logger::LOGGER;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub default_language: String,
    pub default_model: String,
    pub default_timeout: u64,
    pub enable_testing: bool,
    pub enable_debug: bool,
    pub auto_save_code: bool,
    pub output_directory: String,
    pub favorite_models: Vec<String>,
    pub language_preferences: std::collections::HashMap<String, LanguageConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LanguageConfig {
    pub preferred_model: Option<String>,
    pub default_timeout: Option<u64>,
    pub custom_constraints: Option<String>,
    pub enable_testing: Option<bool>,
}

impl Default for Config {
    fn default() -> Self {
        let mut language_preferences = std::collections::HashMap::new();
        
        // Add some sensible defaults for different languages
        language_preferences.insert("rust".to_string(), LanguageConfig {
            preferred_model: Some("deepseek-coder:6.7b".to_string()),
            default_timeout: Some(45),
            custom_constraints: Some("Use idiomatic Rust patterns, handle errors properly".to_string()),
            enable_testing: Some(true),
        });
        
        language_preferences.insert("python".to_string(), LanguageConfig {
            preferred_model: Some("deepseek-coder:1.5b".to_string()),
            default_timeout: Some(30),
            custom_constraints: Some("Follow PEP 8, use type hints".to_string()),
            enable_testing: Some(true),
        });

        Self {
            default_language: "python".to_string(),
            default_model: "deepseek-coder:1.5b".to_string(),
            default_timeout: 30,
            enable_testing: false,
            enable_debug: false,
            auto_save_code: true,
            output_directory: "generated_code".to_string(),
            favorite_models: vec![
                "deepseek-coder:1.5b".to_string(),
                "deepseek-coder:6.7b".to_string(),
                "codellama:7b".to_string(),
                "codellama:13b".to_string(),
            ],
            language_preferences,
        }
    }
}

impl Config {
    pub fn load() -> Self {
        match Self::load_from_file() {
            Ok(config) => {
                LOGGER.debug("Configuration loaded successfully");
                config
            }
            Err(e) => {
                LOGGER.warning(&format!("Failed to load config: {}. Using defaults.", e));
                Self::default()
            }
        }
    }

    pub fn load_from_file() -> Result<Self, Box<dyn std::error::Error>> {
        let config_path = Self::get_config_path()?;
        let content = fs::read_to_string(config_path)?;
        let config: Config = serde_json::from_str(&content)?;
        Ok(config)
    }

    pub fn save(&self) -> Result<(), Box<dyn std::error::Error>> {
        let config_path = Self::get_config_path()?;
        
        // Create config directory if it doesn't exist
        if let Some(parent) = config_path.parent() {
            fs::create_dir_all(parent)?;
        }

        let content = serde_json::to_string_pretty(self)?;
        fs::write(config_path, content)?;
        LOGGER.success("Configuration saved successfully");
        Ok(())
    }

    pub fn get_config_path() -> Result<PathBuf, Box<dyn std::error::Error>> {
        let config_dir = if let Ok(xdg_config) = env::var("XDG_CONFIG_HOME") {
            PathBuf::from(xdg_config)
        } else if let Ok(home) = env::var("HOME") {
            PathBuf::from(home).join(".config")
        } else {
            return Err("Could not determine config directory".into());
        };

        Ok(config_dir.join("alim").join("config.json"))
    }

    pub fn get_language_config(&self, language: &Language) -> LanguageConfig {
        let lang_str = language.to_string().to_lowercase();
        self.language_preferences
            .get(&lang_str)
            .cloned()
            .unwrap_or_default()
    }

    pub fn get_effective_model(&self, language: &Language) -> String {
        let lang_config = self.get_language_config(language);
        lang_config.preferred_model.unwrap_or_else(|| self.default_model.clone())
    }

    pub fn get_effective_timeout(&self, language: &Language) -> u64 {
        let lang_config = self.get_language_config(language);
        lang_config.default_timeout.unwrap_or(self.default_timeout)
    }

    pub fn get_effective_constraints(&self, language: &Language, user_constraints: Option<&str>) -> String {
        if let Some(constraints) = user_constraints {
            if constraints != "None specified" {
                return constraints.to_string();
            }
        }

        let lang_config = self.get_language_config(language);
        lang_config.custom_constraints.unwrap_or_else(|| "None specified".to_string())
    }

    pub fn get_effective_testing(&self, language: &Language) -> bool {
        let lang_config = self.get_language_config(language);
        lang_config.enable_testing.unwrap_or(self.enable_testing)
    }

    pub fn update_language_preference(&mut self, language: &Language, field: &str, value: &str) -> Result<(), String> {
        let lang_str = language.to_string().to_lowercase();
        let lang_config = self.language_preferences
            .entry(lang_str)
            .or_insert_with(LanguageConfig::default);

        match field {
            "model" => lang_config.preferred_model = Some(value.to_string()),
            "timeout" => {
                let timeout: u64 = value.parse().map_err(|_| "Invalid timeout value")?;
                lang_config.default_timeout = Some(timeout);
            }
            "constraints" => lang_config.custom_constraints = Some(value.to_string()),
            "testing" => {
                let enable: bool = value.parse().map_err(|_| "Invalid boolean value")?;
                lang_config.enable_testing = Some(enable);
            }
            _ => return Err(format!("Unknown field: {}", field)),
        }

        Ok(())
    }

    pub fn show_config(&self) {
        LOGGER.section("Current Configuration");
        println!("  Default Language: {}", self.default_language);
        println!("  Default Model: {}", self.default_model);
        println!("  Default Timeout: {}s", self.default_timeout);
        println!("  Testing Enabled: {}", self.enable_testing);
        println!("  Debug Mode: {}", self.enable_debug);
        println!("  Auto-save Code: {}", self.auto_save_code);
        println!("  Output Directory: {}", self.output_directory);
        
        if !self.favorite_models.is_empty() {
            println!("\n  Favorite Models:");
            for model in &self.favorite_models {
                println!("    • {}", model);
            }
        }

        if !self.language_preferences.is_empty() {
            println!("\n  Language-specific Settings:");
            for (lang, config) in &self.language_preferences {
                println!("    {}:", lang);
                if let Some(model) = &config.preferred_model {
                    println!("      Model: {}", model);
                }
                if let Some(timeout) = config.default_timeout {
                    println!("      Timeout: {}s", timeout);
                }
                if let Some(constraints) = &config.custom_constraints {
                    println!("      Constraints: {}", constraints);
                }
                if let Some(testing) = config.enable_testing {
                    println!("      Testing: {}", testing);
                }
            }
        }
    }

    pub fn reset_to_defaults(&mut self) {
        *self = Self::default();
        LOGGER.info("Configuration reset to defaults");
    }
}

impl Default for LanguageConfig {
    fn default() -> Self {
        Self {
            preferred_model: None,
            default_timeout: None,
            custom_constraints: None,
            enable_testing: None,
        }
    }
}

// Configuration management commands for interactive mode
pub fn handle_config_command(args: &[&str], config: &mut Config) -> Result<(), String> {
    if args.is_empty() {
        config.show_config();
        return Ok(());
    }

    match args[0] {
        "show" => config.show_config(),
        "save" => {
            config.save().map_err(|e| format!("Failed to save config: {}", e))?;
        }
        "reset" => {
            config.reset_to_defaults();
        }
        "set" => {
            if args.len() < 3 {
                return Err("Usage: /config set <key> <value>".to_string());
            }
            set_config_value(config, args[1], args[2])?;
        }
        "lang" => {
            if args.len() < 4 {
                return Err("Usage: /config lang <language> <field> <value>".to_string());
            }
            let language = Language::from_str(args[1]).map_err(|e| e.to_string())?;
            config.update_language_preference(&language, args[2], args[3])?;
        }
        _ => return Err(format!("Unknown config command: {}", args[0])),
    }

    Ok(())
}

fn set_config_value(config: &mut Config, key: &str, value: &str) -> Result<(), String> {
    match key {
        "language" => config.default_language = value.to_string(),
        "model" => config.default_model = value.to_string(),
        "timeout" => {
            config.default_timeout = value.parse().map_err(|_| "Invalid timeout value")?;
        }
        "testing" => {
            config.enable_testing = value.parse().map_err(|_| "Invalid boolean value")?;
        }
        "debug" => {
            config.enable_debug = value.parse().map_err(|_| "Invalid boolean value")?;
        }
        "auto_save" => {
            config.auto_save_code = value.parse().map_err(|_| "Invalid boolean value")?;
        }
        "output_dir" => config.output_directory = value.to_string(),
        _ => return Err(format!("Unknown config key: {}", key)),
    }
    Ok(())
}
