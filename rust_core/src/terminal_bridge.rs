use std::process::{<PERSON>, Child, Stdio};
use std::io::{<PERSON><PERSON><PERSON><PERSON>, Buf<PERSON><PERSON>er};
use std::thread;
use std::time::{Duration, Instant};
use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use crate::command_planner::{PlannedCommand, CommandPlan};
use crate::logger::LOGGER;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExecutionResult {
    pub command: String,
    pub exit_code: i32,
    pub stdout: String,
    pub stderr: String,
    pub execution_time: Duration,
    pub timed_out: bool,
    pub success: bool,
}

#[derive(Debug, <PERSON>lone)]
pub struct ExecutionConfig {
    pub timeout_seconds: u64,
    pub capture_output: bool,
    pub working_directory: Option<String>,
    pub environment_vars: HashMap<String, String>,
    pub max_output_lines: Option<usize>,
    pub stream_output: bool,
}

impl Default for ExecutionConfig {
    fn default() -> Self {
        Self {
            timeout_seconds: 30,
            capture_output: true,
            working_directory: None,
            environment_vars: HashMap::new(),
            max_output_lines: Some(1000),
            stream_output: false,
        }
    }
}

pub struct TerminalBridge {
    config: ExecutionConfig,
    active_processes: Arc<Mutex<HashMap<u32, Child>>>,
}

impl TerminalBridge {
    pub fn new(config: ExecutionConfig) -> Self {
        Self {
            config,
            active_processes: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub fn execute_plan(&self, plan: &CommandPlan) -> Vec<ExecutionResult> {
        LOGGER.info(&format!("Executing command plan with {} commands", plan.commands.len()));
        
        let mut results = Vec::new();
        
        for (i, command) in plan.commands.iter().enumerate() {
            LOGGER.info(&format!("Executing command {}/{}: {}", i + 1, plan.commands.len(), command.description));
            
            let result = self.execute_command(command);
            
            // Log result
            if result.success {
                LOGGER.success(&format!("Command completed successfully in {:.2}s", result.execution_time.as_secs_f64()));
            } else {
                LOGGER.error(&format!("Command failed with exit code {} in {:.2}s", result.exit_code, result.execution_time.as_secs_f64()));
                if !result.stderr.is_empty() {
                    LOGGER.error(&format!("Error output: {}", result.stderr));
                }
            }
            
            results.push(result);
            
            // Stop execution if a command fails (unless configured otherwise)
            if !results.last().unwrap().success {
                LOGGER.warning("Stopping execution due to command failure");
                break;
            }
        }
        
        results
    }

    pub fn execute_command(&self, planned_command: &PlannedCommand) -> ExecutionResult {
        let start_time = Instant::now();
        
        let mut command = Command::new(&planned_command.command);
        command.args(&planned_command.args);
        
        // Set working directory
        if let Some(ref wd) = planned_command.working_directory {
            command.current_dir(wd);
        } else if let Some(ref wd) = self.config.working_directory {
            command.current_dir(wd);
        } else {
            // Use platform-specific current directory
            command.current_dir(platform::get_current_directory());
        }
        
        // Set environment variables
        for (key, value) in &planned_command.environment_vars {
            command.env(key, value);
        }
        for (key, value) in &self.config.environment_vars {
            command.env(key, value);
        }
        
        // Configure stdio
        if self.config.capture_output {
            command.stdout(Stdio::piped());
            command.stderr(Stdio::piped());
        }
        
        // Execute with timeout
        let timeout = Duration::from_secs(
            planned_command.timeout_seconds.unwrap_or(self.config.timeout_seconds)
        );
        
        match self.execute_with_timeout(command, timeout) {
            Ok(result) => {
                let execution_time = start_time.elapsed();
                ExecutionResult {
                    command: format!("{} {}", planned_command.command, planned_command.args.join(" ")),
                    exit_code: result.0,
                    stdout: result.1,
                    stderr: result.2,
                    execution_time,
                    timed_out: false,
                    success: result.0 == 0,
                }
            }
            Err(e) => {
                let execution_time = start_time.elapsed();
                LOGGER.error(&format!("Command execution error: {}", e));
                ExecutionResult {
                    command: format!("{} {}", planned_command.command, planned_command.args.join(" ")),
                    exit_code: -1,
                    stdout: String::new(),
                    stderr: e.to_string(),
                    execution_time,
                    timed_out: execution_time >= timeout,
                    success: false,
                }
            }
        }
    }

    fn execute_with_timeout(&self, mut command: Command, timeout: Duration) -> Result<(i32, String, String), Box<dyn std::error::Error>> {
        let mut child = command.spawn()?;
        let child_id = child.id();
        
        // Store the process for potential cleanup
        {
            let mut processes = self.active_processes.lock().unwrap();
            processes.insert(child_id, child);
        }
        
        // Wait for completion with timeout
        let start_time = Instant::now();
        let mut stdout_output = String::new();
        let mut stderr_output = String::new();
        
        // Handle streaming output if enabled
        if self.config.stream_output {
            self.stream_process_output(child_id, timeout)?;
        }
        
        // Wait for process completion
        loop {
            let mut processes = self.active_processes.lock().unwrap();
            if let Some(mut process) = processes.remove(&child_id) {
                match process.try_wait()? {
                    Some(status) => {
                        // Process completed
                        if self.config.capture_output {
                            if let Some(stdout) = process.stdout.take() {
                                let reader = BufReader::new(stdout);
                                for line in reader.lines() {
                                    if let Ok(line) = line {
                                        stdout_output.push_str(&line);
                                        stdout_output.push('\n');
                                        
                                        // Limit output size
                                        if let Some(max_lines) = self.config.max_output_lines {
                                            let line_count = stdout_output.lines().count();
                                            if line_count > max_lines {
                                                stdout_output = stdout_output.lines()
                                                    .skip(line_count - max_lines)
                                                    .collect::<Vec<_>>()
                                                    .join("\n");
                                                stdout_output.push_str("\n... (output truncated) ...\n");
                                            }
                                        }
                                    }
                                }
                            }
                            
                            if let Some(stderr) = process.stderr.take() {
                                let reader = BufReader::new(stderr);
                                for line in reader.lines() {
                                    if let Ok(line) = line {
                                        stderr_output.push_str(&line);
                                        stderr_output.push('\n');
                                    }
                                }
                            }
                        }
                        
                        let exit_code = status.code().unwrap_or(-1);
                        return Ok((exit_code, stdout_output, stderr_output));
                    }
                    None => {
                        // Process still running, check timeout
                        if start_time.elapsed() >= timeout {
                            LOGGER.warning(&format!("Command timed out after {:.2}s, terminating...", timeout.as_secs_f64()));
                            let _ = process.kill();
                            let _ = process.wait();
                            return Err("Command timed out".into());
                        }
                        
                        // Put process back and continue waiting
                        processes.insert(child_id, process);
                        drop(processes);
                        thread::sleep(Duration::from_millis(100));
                    }
                }
            } else {
                return Err("Process not found".into());
            }
        }
    }

    fn stream_process_output(&self, child_id: u32, _timeout: Duration) -> Result<(), Box<dyn std::error::Error>> {
        // This would implement real-time output streaming
        // For now, we'll keep it simple and just log that streaming is enabled
        LOGGER.debug(&format!("Streaming output for process {}", child_id));
        Ok(())
    }

    pub fn kill_process(&self, child_id: u32) -> Result<(), Box<dyn std::error::Error>> {
        let mut processes = self.active_processes.lock().unwrap();
        if let Some(mut process) = processes.remove(&child_id) {
            process.kill()?;
            process.wait()?;
            LOGGER.info(&format!("Process {} terminated", child_id));
            Ok(())
        } else {
            Err("Process not found".into())
        }
    }

    pub fn list_active_processes(&self) -> Vec<u32> {
        let processes = self.active_processes.lock().unwrap();
        processes.keys().cloned().collect()
    }

    pub fn execute_interactive_command(&self, command_line: &str) -> ExecutionResult {
        let parts: Vec<&str> = command_line.split_whitespace().collect();
        if parts.is_empty() {
            return ExecutionResult {
                command: command_line.to_string(),
                exit_code: 1,
                stdout: String::new(),
                stderr: "Empty command".to_string(),
                execution_time: Duration::from_secs(0),
                timed_out: false,
                success: false,
            };
        }

        let planned_command = PlannedCommand {
            command: parts[0].to_string(),
            args: parts[1..].iter().map(|s| s.to_string()).collect(),
            description: format!("Interactive command: {}", command_line),
            expected_output: None,
            working_directory: None,
            environment_vars: HashMap::new(),
            timeout_seconds: Some(30),
        };

        self.execute_command(&planned_command)
    }

    pub fn update_config(&mut self, config: ExecutionConfig) {
        self.config = config;
    }
}

// Cross-platform command utilities
pub mod platform {
    use std::env;

    pub fn get_shell_command() -> (&'static str, Vec<&'static str>) {
        if cfg!(target_os = "windows") {
            ("cmd", vec!["/C"])
        } else {
            ("sh", vec!["-c"])
        }
    }

    pub fn get_current_directory() -> String {
        env::current_dir()
            .map(|p| p.to_string_lossy().to_string())
            .unwrap_or_else(|_| ".".to_string())
    }

    pub fn is_dangerous_command(command: &str) -> bool {
        let dangerous_commands = [
            "rm", "del", "format", "fdisk", "mkfs",
            "dd", "shutdown", "reboot", "halt",
            "chmod 777", "chown", "passwd",
        ];
        
        let command_lower = command.to_lowercase();
        dangerous_commands.iter().any(|&dangerous| command_lower.contains(dangerous))
    }
}

impl std::fmt::Display for ExecutionResult {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Command: {}\nExit Code: {}\nExecution Time: {:.2}s\nSuccess: {}\n",
               self.command, self.exit_code, self.execution_time.as_secs_f64(), self.success)?;
        
        if !self.stdout.is_empty() {
            write!(f, "STDOUT:\n{}\n", self.stdout)?;
        }
        
        if !self.stderr.is_empty() {
            write!(f, "STDERR:\n{}\n", self.stderr)?;
        }
        
        Ok(())
    }
}
