use std::process::{Command, Child, Output};
use std::fs;
use std::time::{Duration, Instant};
use std::thread;
use std::io;
use crate::language::Language;
use crate::logger::LOGGER;

pub struct ExecutionConfig {
    pub timeout_seconds: u64,
    pub max_memory_mb: Option<u64>,
}

impl Default for ExecutionConfig {
    fn default() -> Self {
        Self {
            timeout_seconds: 30, // 30 second default timeout
            max_memory_mb: Some(512), // 512MB memory limit
        }
    }
}

pub fn run_generated_code(language: &Language) {
    run_generated_code_with_config(language, &ExecutionConfig::default());
}

pub fn run_generated_code_with_config(language: &Language, config: &ExecutionConfig) {
    let filename = language.output_filename();

    // First, let's show the generated code
    match fs::read_to_string(&filename) {
        Ok(code) => {
            LOGGER.section(&format!("Generated {} Code", language));
            println!("{}", code);
            LOGGER.separator();
        }
        Err(e) => {
            LOGGER.error(&format!("Failed to read generated code from {}: {}", filename, e));
            return;
        }
    }

    // Compile if needed
    if let Some(compile_cmd) = language.compile_command(&filename) {
        LOGGER.compilation();

        let mut compile_process = Command::new(&compile_cmd[0])
            .args(&compile_cmd[1..])
            .spawn()
            .expect("Failed to start compile command");

        // Wait for compilation with timeout
        let compile_result = wait_for_process(compile_process, Duration::from_secs(60));

        match compile_result {
            Ok(output) => {
                if !output.status.success() {
                    let stderr = String::from_utf8_lossy(&output.stderr);
                    eprintln!("\n❌ Compilation Error:");
                    eprintln!("{}", stderr);
                    return;
                }
                println!("✅ Compilation successful!");
            }
            Err(e) => {
                eprintln!("\n❌ Compilation timeout or error: {}", e);
                return;
            }
        }
    }

    // Execute the code with timeout
    let exec_cmd = language.execute_command(&filename);
    println!("⏱️  Executing with {}s timeout...", config.timeout_seconds);

    let mut exec_process = Command::new(&exec_cmd[0])
        .args(&exec_cmd[1..])
        .spawn()
        .expect("Failed to start execution command");

    let exec_result = wait_for_process(exec_process, Duration::from_secs(config.timeout_seconds));

    match exec_result {
        Ok(output) => {
            if output.status.success() {
                let stdout = String::from_utf8_lossy(&output.stdout);
                if !stdout.trim().is_empty() {
                    println!("\n✅ Output:");
                    println!("{}", stdout);
                } else {
                    println!("\n✅ Code executed successfully (no output)");
                }
            } else {
                let stderr = String::from_utf8_lossy(&output.stderr);
                eprintln!("\n❌ Execution Error:");
                eprintln!("{}", stderr);
            }
        }
        Err(e) => {
            eprintln!("\n❌ Execution timeout or error: {}", e);
            eprintln!("🛑 Process terminated due to timeout ({}s)", config.timeout_seconds);
        }
    }
}

fn wait_for_process(mut process: Child, timeout: Duration) -> Result<Output, io::Error> {
    let start_time = Instant::now();

    loop {
        match process.try_wait() {
            Ok(Some(_)) => {
                // Process has finished, get the output
                return process.wait_with_output();
            }
            Ok(None) => {
                // Process is still running, check timeout
                if start_time.elapsed() >= timeout {
                    let _ = process.kill();
                    return Err(io::Error::new(
                        io::ErrorKind::TimedOut,
                        "Process execution timed out"
                    ));
                }
                // Sleep briefly before checking again
                thread::sleep(Duration::from_millis(100));
            }
            Err(e) => {
                return Err(e);
            }
        }
    }
}

pub fn run_tests(language: &Language, task: &str) {
    println!("\n🧪 Running tests...");

    let code_file = match language {
        Language::Python => "generated_code.py",
        Language::JavaScript => "generated_code.js",
        Language::TypeScript => "generated_code.ts",
        Language::Rust => "generated_code.rs",
        Language::Go => "generated_code.go",
        Language::Java => "generated_code.java",
        Language::CSharp => "generated_code.cs",
        Language::Cpp => "generated_code.cpp",
        Language::C => "generated_code.c",
    };

    let status = Command::new("python3")
        .arg("py_helpers/test_runner.py")
        .arg(code_file)
        .arg(&language.to_string().to_lowercase())
        .arg(task)
        .status()
        .expect("Failed to run test_runner.py");

    if status.success() {
        println!("✅ All tests passed!");
    } else {
        println!("❌ Some tests failed or encountered errors.");
    }
}
