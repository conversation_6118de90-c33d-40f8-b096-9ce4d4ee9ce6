use std::process::Command;
use crate::language::Language;

pub fn generate_code_via_python(task: &str, model: &str, language: &Language, constraints: &str) {
    println!("🔄 Generating code...");

    let status = Command::new("python3")
        .arg("py_helpers/gen_code.py")
        .arg(task)
        .arg(model)
        .arg(&language.to_string().to_lowercase())
        .arg(constraints)
        .status()
        .expect("Failed to run gen_code.py");

    if !status.success() {
        eprintln!("❌ Code generation failed.");
        std::process::exit(1);
    } else {
        println!("✅ Code generated successfully!");
    }
}
