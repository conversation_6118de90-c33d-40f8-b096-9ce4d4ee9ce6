mod agent;
mod runner;
mod language;
mod logger;
mod config;
mod help;
mod command_planner;
mod terminal_bridge;
mod command_memory;
mod interactive_handler;
mod security_layer;

use clap::{Arg, Command};
use language::{Language, list_supported_languages};
use logger::LOGGER;
use config::Config;
use interactive_handler::InteractiveSession;
use std::io::{self, Write};

fn main() {
    let config = Config::load();

    let matches = Command::new("Alim Agent")
        .version("0.1.0")
        .author("Your Name")
        .about("A CLI-based code generation agent")
        .arg(
            Arg::new("task")
                .short('t')
                .long("task")
                .value_name("TASK")
                .help("The coding task to execute")
                .required(false),
        )
        .arg(
            Arg::new("interactive")
                .short('i')
                .long("interactive")
                .help("Run in interactive mode")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("enhanced-interactive")
                .long("enhanced")
                .help("Run in enhanced interactive mode with terminal integration")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("demo")
                .long("demo")
                .help("Run a demonstration of all terminal interaction features")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("model")
                .short('m')
                .long("model")
                .value_name("MODEL")
                .help("Specify the model to use")
                .default_value("deepseek-coder:1.5b"),
        )
        .arg(
            Arg::new("language")
                .short('l')
                .long("language")
                .value_name("LANGUAGE")
                .help("Programming language to generate code in")
                .default_value("python"),
        )
        .arg(
            Arg::new("list-languages")
                .long("list-languages")
                .help("List all supported programming languages")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("constraints")
                .short('c')
                .long("constraints")
                .value_name("CONSTRAINTS")
                .help("Additional constraints or requirements for the code")
                .default_value("None specified"),
        )
        .arg(
            Arg::new("timeout")
                .long("timeout")
                .value_name("SECONDS")
                .help("Execution timeout in seconds")
                .default_value("30"),
        )
        .arg(
            Arg::new("test")
                .long("test")
                .help("Run tests after code generation")
                .action(clap::ArgAction::SetTrue),
        )
        .arg(
            Arg::new("help-topic")
                .long("help-topic")
                .value_name("TOPIC")
                .help("Show detailed help for a specific topic")
                .value_parser(clap::value_parser!(String)),
        )
        .get_matches();

    if matches.get_flag("list-languages") {
        list_languages();
    } else if let Some(topic) = matches.get_one::<String>("help-topic") {
        help::show_topic_help(topic);
    } else if matches.get_flag("demo") {
        run_terminal_interaction_demo(config);
    } else if matches.get_flag("enhanced-interactive") {
        run_enhanced_interactive_mode(config);
    } else if matches.get_flag("interactive") {
        run_interactive_mode(config);
    } else if let Some(task) = matches.get_one::<String>("task") {
        let model = matches.get_one::<String>("model").unwrap();
        let language_str = matches.get_one::<String>("language").unwrap();

        let constraints = matches.get_one::<String>("constraints").unwrap();
        let timeout_str = matches.get_one::<String>("timeout").unwrap();
        let timeout = timeout_str.parse::<u64>().unwrap_or(30);
        let run_tests = matches.get_flag("test");

        match Language::from_str(language_str) {
            Ok(language) => execute_task(task, model, &language, constraints, timeout, run_tests),
            Err(e) => {
                eprintln!("❌ {}", e);
                eprintln!("💡 Use --list-languages to see supported languages");
                std::process::exit(1);
            }
        }
    } else {
        println!("Please provide a task with -t/--task or use -i/--interactive mode");
        println!("Use --help for more information");
    }
}

fn execute_task(task: &str, model: &str, language: &Language, constraints: &str, timeout: u64, run_tests: bool) {
    LOGGER.task_start(task);
    LOGGER.settings(&language.to_string(), model, timeout, run_tests);

    if constraints != "None specified" {
        LOGGER.info(&format!("Constraints: {}", constraints));
    }

    agent::generate_code_via_python(task, model, language, constraints);

    let config = runner::ExecutionConfig {
        timeout_seconds: timeout,
        max_memory_mb: Some(512),
    };
    runner::run_generated_code_with_config(language, &config);

    if run_tests {
        runner::run_tests(language, task);
    }

    LOGGER.task_complete(task);
}

fn list_languages() {
    println!("🔧 Supported Programming Languages:");
    println!("{}", "=".repeat(40));

    for lang in list_supported_languages() {
        println!("  • {} (.{})", lang, lang.file_extension());
    }

    println!("\n💡 Usage examples:");
    println!("  rust_core -t \"fibonacci function\" -l python");
    println!("  rust_core -t \"hello world\" -l rust");
    println!("  rust_core -t \"web server\" -l javascript");
}

fn run_interactive_mode(mut config: Config) {
    LOGGER.welcome();
    LOGGER.info("Interactive Mode - Type 'help' for commands, 'quit' to exit");
    LOGGER.separator();

    let mut current_language = Language::from_str(&config.default_language).unwrap_or(Language::Python);
    let mut current_model = config.get_effective_model(&current_language);
    let mut current_timeout = config.get_effective_timeout(&current_language);
    let mut run_tests = config.get_effective_testing(&current_language);

    loop {
        print!("\n");
        LOGGER.interactive_prompt(&current_language.to_string());
        io::stdout().flush().unwrap();

        let mut input = String::new();
        match io::stdin().read_line(&mut input) {
            Ok(_) => {
                let command = input.trim();

                if command.is_empty() {
                    continue;
                }

                // Handle slash commands
                if command.starts_with("/") {
                    handle_interactive_command(command, &mut current_language, &mut current_model, &mut current_timeout, &mut run_tests, &mut config);
                    continue;
                }

                match command.to_lowercase().as_str() {
                    "exit" | "quit" => {
                        LOGGER.goodbye();
                        break;
                    }
                    "help" => {
                        show_interactive_help();
                    }
                    "status" => {
                        show_current_settings(&current_language, &current_model, current_timeout, run_tests);
                    }
                    _ => {
                        execute_task(command, &current_model, &current_language, "None specified", current_timeout, run_tests);
                    }
                }
            }
            Err(error) => {
                eprintln!("❌ Error reading input: {}", error);
                break;
            }
        }
    }
}

fn show_interactive_help() {
    println!("\n📚 Available commands:");
    println!("  • Enter any coding task to generate and run code");
    println!("  • 'help' - Show this help message");
    println!("  • 'status' - Show current settings");
    println!("  • 'exit' or 'quit' - Exit interactive mode");
    println!("\n⚙️  Configuration commands:");
    println!("  • /lang <language> - Change programming language");
    println!("  • /model <model> - Change AI model");
    println!("  • /timeout <seconds> - Change execution timeout");
    println!("  • /test on|off - Enable/disable testing");
    println!("  • /languages - List supported languages");
    println!("  • /config [show|save|reset] - Manage configuration");
    println!("  • /config set <key> <value> - Set config value");
    println!("  • /config lang <lang> <field> <value> - Set language-specific config");
    println!("  • /help [topic] - Show detailed help (topics: basic, examples, etc.)");
    println!("\n💡 Examples:");
    println!("  fibonacci function");
    println!("  /lang rust");
    println!("  /test on");
    println!("  web server with error handling");
}

fn show_current_settings(language: &Language, model: &str, timeout: u64, run_tests: bool) {
    LOGGER.settings(&language.to_string(), model, timeout, run_tests);
}

fn run_enhanced_interactive_mode(config: Config) {
    LOGGER.info("Starting enhanced interactive mode with terminal integration...");

    match InteractiveSession::new(config.clone()) {
        Ok(mut session) => {
            if let Err(e) = session.run() {
                LOGGER.error(&format!("Enhanced interactive session error: {}", e));
            }
        }
        Err(e) => {
            LOGGER.error(&format!("Failed to initialize enhanced interactive session: {}", e));
            LOGGER.info("Falling back to standard interactive mode...");
            run_interactive_mode(config);
        }
    }
}

fn run_terminal_interaction_demo(_config: Config) {
    use command_planner::{CommandPlanner, CommandContext};
    use terminal_bridge::{TerminalBridge, ExecutionConfig};
    use command_memory::CommandMemory;
    use security_layer::SecurityLayer;
    use std::collections::HashMap;

    LOGGER.task_start("Terminal Interaction System Demo");

    // 1. LLM as Planner Demo
    LOGGER.section("1. LLM Command Planning");
    let context = CommandContext {
        current_directory: std::env::current_dir().unwrap().to_string_lossy().to_string(),
        available_tools: vec!["ls".to_string(), "python3".to_string(), "git".to_string()],
        recent_commands: Vec::new(),
        project_type: Some("rust".to_string()),
        language_context: Some("python".to_string()),
    };

    let planner = CommandPlanner::new(context);
    let demo_inputs = vec![
        "list files in current directory",
        "show system memory usage",
        "create a fibonacci function",
        "git status",
    ];

    for input in demo_inputs {
        println!("\n📝 Input: {}", input);
        let plan = planner.plan_command(input);
        println!("   Intent: {} | Risk: {} | Commands: {}",
            plan.intent, plan.risk_level, plan.commands.len());
        for cmd in &plan.commands {
            println!("   → {} {}", cmd.command, cmd.args.join(" "));
        }
    }

    // 2. Terminal Bridge Demo
    LOGGER.section("2. Terminal Bridge Execution");
    let execution_config = ExecutionConfig {
        timeout_seconds: 10,
        capture_output: true,
        working_directory: None,
        environment_vars: HashMap::new(),
        max_output_lines: Some(10),
        stream_output: false,
    };

    let bridge = TerminalBridge::new(execution_config);
    let safe_plan = planner.plan_command("list files in current directory");

    println!("Executing safe command plan...");
    let results = bridge.execute_plan(&safe_plan);
    for result in results {
        if result.success {
            println!("✅ Command succeeded in {:.2}s", result.execution_time.as_secs_f64());
            if !result.stdout.is_empty() {
                println!("Output: {}", result.stdout.lines().take(3).collect::<Vec<_>>().join("\n"));
            }
        } else {
            println!("❌ Command failed: {}", result.stderr);
        }
    }

    // 3. Security Layer Demo
    LOGGER.section("3. Security Layer Protection");
    let config_dir = Config::get_config_path().unwrap().parent().unwrap().to_path_buf();
    match SecurityLayer::new(config_dir) {
        Ok(mut security) => {
            security.show_policy();

            // Test dangerous command
            let dangerous_plan = planner.plan_command("delete all files");
            let security_check = security.check_command_plan(&dangerous_plan);

            println!("\n🔒 Security check for dangerous command:");
            println!("   Allowed: {}", security_check.allowed);
            for violation in security_check.violations {
                println!("   ⚠️  {}", violation);
            }
        }
        Err(e) => {
            LOGGER.error(&format!("Failed to initialize security layer: {}", e));
        }
    }

    // 4. Command Memory Demo
    LOGGER.section("4. Command Memory & History");
    let data_dir = Config::get_config_path().unwrap().parent().unwrap().join("data");
    match CommandMemory::new(data_dir) {
        Ok(memory) => {
            let suggestions = memory.get_suggestions("list files");
            if !suggestions.is_empty() {
                println!("💡 Suggestions for 'list files': {}", suggestions.join(", "));
            } else {
                println!("💡 No suggestions available yet (empty history)");
            }
            memory.show_history(Some(5));
        }
        Err(e) => {
            LOGGER.error(&format!("Failed to initialize command memory: {}", e));
        }
    }

    LOGGER.task_complete("Terminal Interaction System Demo");

    println!("\n🚀 Try the enhanced interactive mode with: --enhanced");
    println!("   This demo showed all 5 components working together:");
    println!("   1. ✅ LLM Command Planning");
    println!("   2. ✅ Terminal Bridge Execution");
    println!("   3. ✅ Security Layer Protection");
    println!("   4. ✅ Command Memory & History");
    println!("   5. ✅ Enhanced Interactive Handling (available in --enhanced mode)");
}

fn handle_interactive_command(command: &str, language: &mut Language, model: &mut String, timeout: &mut u64, run_tests: &mut bool, config: &mut Config) {
    let parts: Vec<&str> = command.split_whitespace().collect();
    if parts.is_empty() {
        return;
    }

    match parts[0] {
        "/lang" | "/language" => {
            if parts.len() < 2 {
                println!("❌ Usage: /lang <language>");
                return;
            }
            match Language::from_str(parts[1]) {
                Ok(new_lang) => {
                    *language = new_lang;
                    println!("✅ Language changed to: {}", language);
                }
                Err(e) => {
                    println!("❌ {}", e);
                    println!("💡 Use /languages to see supported languages");
                }
            }
        }
        "/model" => {
            if parts.len() < 2 {
                println!("❌ Usage: /model <model_name>");
                return;
            }
            *model = parts[1..].join(" ");
            println!("✅ Model changed to: {}", model);
        }
        "/timeout" => {
            if parts.len() < 2 {
                println!("❌ Usage: /timeout <seconds>");
                return;
            }
            match parts[1].parse::<u64>() {
                Ok(new_timeout) => {
                    *timeout = new_timeout;
                    println!("✅ Timeout changed to: {}s", timeout);
                }
                Err(_) => {
                    println!("❌ Invalid timeout value. Please enter a number.");
                }
            }
        }
        "/test" => {
            if parts.len() < 2 {
                println!("❌ Usage: /test on|off");
                return;
            }
            match parts[1].to_lowercase().as_str() {
                "on" | "true" | "1" | "yes" => {
                    *run_tests = true;
                    println!("✅ Testing enabled");
                }
                "off" | "false" | "0" | "no" => {
                    *run_tests = false;
                    println!("✅ Testing disabled");
                }
                _ => {
                    println!("❌ Invalid value. Use 'on' or 'off'");
                }
            }
        }
        "/languages" => {
            list_languages();
        }
        "/config" => {
            match config::handle_config_command(&parts[1..], config) {
                Ok(()) => {}
                Err(e) => LOGGER.error(&e),
            }
        }
        "/help" => {
            if parts.len() > 1 {
                help::show_topic_help(parts[1]);
            } else {
                help::show_help_menu();
            }
        }
        _ => {
            println!("❌ Unknown command: {}", parts[0]);
            println!("💡 Type 'help' for available commands");
        }
    }
}
